/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { FileWithPreview } from '../types/file';
import ExifReader from 'exifreader';
import { readFilePromise } from './files';

const convertToDecimalDegree = (gpsDesc: string | undefined, direction: string | undefined) => {
  if (!gpsDesc) {
    return;
  }
  // Don't do anything for N or E
  return direction === 'S' || direction === 'W' ? Number(gpsDesc) * -1 : Number(gpsDesc);
};

export const getFileData = async (file: FileWithPreview): Promise<FileWithPreview> => {
  if (!file || !file.type.startsWith('image')) {
    return file;
  }

  const tags = await ExifReader.load(file, { expanded: true }).catch(() => ({ exif: undefined }));
  if (tags.exif) {
    const { GPSLatitude, GPSLongitude, GPSLatitudeRef, GPSLongitudeRef } = tags.exif;
    file.Latitude = convertToDecimalDegree(GPSLatitude?.description, GPSLatitudeRef?.value?.[0]);
    file.Longitude = convertToDecimalDegree(GPSLongitude?.description, GPSLongitudeRef?.value?.[0]);
  }
  file.preview = URL.createObjectURL(file);
  return file;
}


export const base64PrefixRegex = /^data:image\/\w+;base64,/;

const svgBase64PrefixRegex = /^data:image\/\w+\+?\w+;base64,/;

export const isSVGBase64URL = (imgBase64: string) => imgBase64.match(svgBase64PrefixRegex);

const svgUrlPattern = /(\.svg(\?[^#]*)?(#.*)?)$/i;
export const isSvgUrl = (url: string) => svgUrlPattern.test(url);

export const getImageBase64FromSVGBase64 = async ({
  imgBase64,
  removeBase64Prefix = false,
}: {
  imgBase64: string;
  removeBase64Prefix?: boolean;
}) => {
  if (imgBase64 === 'data:') {
    return '';
  }
  // no need to convert if it's already supported png/jpeg image
  if (!isSVGBase64URL(imgBase64)) {
    return imgBase64;
  }

  const image = new Image();
  image.src = imgBase64;
  const png: string = await new Promise((resolve) => {
    image.onload = () => {
      const canvas = window.document.createElement('canvas');
      canvas.width = image.width;
      canvas.height = image.height;

      const context = canvas.getContext('2d');
      context?.drawImage(image, 0, 0);
      resolve(canvas.toDataURL());
    };
  });

  if (removeBase64Prefix) {
    return png.replace(base64PrefixRegex, '');
  }

  return png;
};

export const getImageBase64FromSVGUrl = async (sdgImgSrc: string) => {
  const img = await readFilePromise(sdgImgSrc);

  if (img === 'data:') {
    return;
  }

  return getImageBase64FromSVGBase64({ imgBase64: img });
};

/**
 * Loads an SVG file as a Buffer/Uint8Array for use with docx package in client-side environments.
 *
 * @param svgUrl - URL or path to the SVG file
 * @returns Promise<Uint8Array> - SVG content as Uint8Array suitable for docx ImageRun
 */
export const loadSVGFromUrl = async (svgUrl: string): Promise<Uint8Array> => {
  try {
    const response = await fetch(svgUrl, {
      mode: 'no-cors',
      headers: [],
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch SVG: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    return new Uint8Array(arrayBuffer);
  } catch (error) {
    console.error(`Failed to load SVG from ${svgUrl}:`, error);
    throw error;
  }
};
